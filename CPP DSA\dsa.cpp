#include <IOSTREAM>
using namespace std;

	
void acceptList(int n,int arr[])
{

	for (int i=0; i<n; i++)
	{
		cout<<"Enter number "<< i+1<<": ";
		cin>>arr[i];	
		
	}
}


int linearSearch(int n, int arr[],int target) {

	
	for(int i=0;i<n; i++) {
		if (arr[i]==target)
		{
			return i;
		}
	}
	
	return -1;
}

int binaryS(int n,int arr[],int target){
	int low=0;
	int high=n-1;
	while(low<=high){
	
		int mid=low+(high-low)/2;
		if (arr[mid]==target) {
			return mid;
		} else if( arr[mid]<target) {
			low=mid-1;
		} else {
				high=mid-1;	
		}
	}
	return -1;
	
	
}

void insertion_sort(int n,int arr[])
{
	int temp;
	for(int i=1;i<n;i++){
		temp = arr[i];
		for(int j=i; j>0 && temp<arr[j-1]; j--)
		{
			arr[j] = arr[j-1];
			arr[j-1] = temp;
		}
	}
}


void selection_sort(int n,int arr[])
{
	int i, j, smallest, temp;
	for(int i=0;i<n;i++){
		smallest = i;
		for(j=i+1; j<n; j++) {
			if(arr[j] < arr[smallest])
			{
				smallest = j;
				
				temp = arr[smallest];
				arr[smallest] = arr[i];
				arr[i] = temp;
			}

		}
	}
}

void bubble_sort(int n,int arr[])
{
	int i, j, temp;
	for(int i=0;i<n;i++){
		for(j=n-1; j>i; j--) {
			if(arr[j] < arr[j-1])
			{
				temp = arr[j];
				arr[j] = arr[j-1];
				arr[j-1] = temp;
			}

		}
	}
}

int main()
{	
	int n;
	cout<<"Enter size of the array: ";
	cin>>n;
	int arr[n];
	int target;
	cout<<"Enter target: ";
	cin>>target;
	
	acceptList(n, arr);
	
	int op;
	cout<<"enter your choice"<<endl;
	cout<<"enter 1 For Linear Search"<<endl;
	cout<<"enter 2 For Binary Search"<<endl;
	cout<<"enter 3 For Insertion Sort"<<endl;
	cout<<"enter 4 For Selection Sort"<<endl;
	cout<<"enter 5 For Bubble Sort"<<endl;			
	cin>>op;
	switch(op)
	{
	case 1:
		cout<<linearSearch(n, arr, target)<<endl;
		break;
	case 2:
		cout<<binaryS(n,arr,target)<<endl;
		break;
	case 3:
		insertion_sort(n, arr);
		
		cout<<"array after sort: ";
		for(int i=0; i<n; i++)
		{
			cout<<arr[i]<< ",";
		}
		break;
	case 4:
		selection_sort(n, arr);
		
		cout<<"array after sort: ";
		for(int i=0; i<n; i++)
		{
			cout<<arr[i]<< ",";
		}
		break;
	case 5:
		bubble_sort(n, arr);
		
		cout<<"array after sort: ";
		for(int i=0; i<n; i++)
		{
			cout<<arr[i]<< ",";
		}
		break;
	default:
		cout<<"wrong option"<<endl;
		break;
	}

	return 0;
}