# Demo Instructions for Group 2 Doubly Circular Attendance System

## Quick Test Scenario

### Step 1: Add Sample Employees
```
Choice: 1 (Add Employee)
ID: 101, Name: <PERSON> Yehualashet
ID: 102, Name: <PERSON>  
ID: 103, Name: Temesgen Abebe
ID: 104, Name: <PERSON><PERSON><PERSON><PERSON> Nigatu
ID: 105, Name: <PERSON><PERSON><PERSON>
```

### Step 2: Check-in Some Employees
```
Choice: 2 (Check-in Employee)
ID: 101 (<PERSON> checks in)
ID: 103 (<PERSON><PERSON><PERSON> checks in)
ID: 105 (<PERSON><PERSON><PERSON> checks in)
```

### Step 3: Display All Employees
```
Choice: 5 (Display All Employees)
- Shows all employees with their attendance status
```

### Step 4: Search for an Employee
```
Choice: 4 (Search Employee)
ID: 102 (Search for Abel)
```

### Step 5: Check-out an Employee
```
Choice: 3 (Check-out Employee)
ID: 101 (<PERSON> checks out)
```

### Step 6: Update Employee Name
```
Choice: 6 (Update Employee Name)
ID: 102, New Name: <PERSON>
```

### Step 7: Sort Employees by ID
```
Choice: 8 (Sort Employees by ID)
- Arranges employees in ascending order by ID
```

### Step 8: Delete an Employee
```
Choice: 7 (Delete Employee)
ID: 104 (Remove Mihretab)
```

### Step 9: Final Display
```
Choice: 5 (Display All Employees)
- Shows final state after all operations
```

## Key Features Demonstrated
1. **Doubly Circular Structure**: Navigation in both directions
2. **Check-in/Check-out**: Automatic timestamp recording
3. **Search Functionality**: Quick employee lookup
4. **Sorting**: Simple bubble sort implementation
5. **CRUD Operations**: Create, Read, Update, Delete
6. **Memory Management**: Proper cleanup on deletion

## Expected Output Features
- Real-time timestamps for check-in/check-out
- Clear status indicators (Present/Absent)
- Sorted display by employee ID
- Error handling for invalid operations
- User-friendly menu interface

## Notes for Presentation
- Emphasize the circular nature of the linked list
- Show how doubly-linked structure allows bidirectional traversal
- Demonstrate the simplicity of the algorithms used
- Highlight the practical application for attendance management
