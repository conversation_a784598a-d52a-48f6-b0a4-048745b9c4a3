# Group 2 - Doubly Circular Linked List Employee Attendance System

## Group Members
1. <PERSON> Yehualashet [IHRCS-352438-16]
2. <PERSON> [IHRCS-9781-16]
3. TEMESGEN ABEBE [IHRCS-328669-16]
4. MIHRETAB NIGATU [IHRCS-9599-16]
5. ER<PERSON>AS GIRMA [IHRCS-829949-16]
6. <PERSON><PERSON><PERSON><PERSON> Dukamo [907359/16]

## Project Description
This project implements a simple doubly-circular linked list to handle employee attendance management for an organization. The system supports check-in and check-out operations along with basic CRUD operations.

## Features
- **Add Employee**: Register new employees in the system
- **Check-in**: Record employee arrival time
- **Check-out**: Record employee departure time
- **Search**: Find employee by ID
- **Display All**: Show all employees and their attendance status
- **Update**: Modify employee information
- **Delete**: Remove employee from system
- **Sort**: Arrange employees by ID using bubble sort
- **Sample Data**: Pre-loaded with 6 group members for testing
- **Test Demo**: Automated demonstration of all functions

## Data Structure
- **Doubly Circular Linked List**: Each node contains employee information and pointers to both next and previous nodes
- **Circular**: Last node points back to first node, first node's previous points to last node
- **Simple Implementation**: Basic algorithms with clear logic, no complex optimizations

## How to Compile and Run
```bash
g++ doubly_circular_attendance.cpp -o attendance
./attendance
```

## Menu Options
1. Add Employee
2. Check-in Employee
3. Check-out Employee
4. Search Employee
5. Display All Employees
6. Update Employee Name
7. Delete Employee
8. Sort Employees by ID
9. Run Test Demo
10. Exit

## Implementation Details
- Uses simple bubble sort algorithm for sorting
- Automatic timestamp generation for check-in/check-out
- Basic error handling for common scenarios
- Memory management with proper destructor
- Menu-driven interface for easy interaction

## Code Structure
- **Employee struct**: Contains employee data and pointers
- **AttendanceSystem class**: Manages the doubly circular linked list
- **Simple algorithms**: Basic implementations for educational purposes
- **Clear comments**: Well-documented code for easy understanding
