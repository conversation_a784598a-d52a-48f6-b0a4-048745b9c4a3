// Group Members:
// 1. Mihretab Nigatu 9599-16
// 2. Temesgen Abebe 328669-16
// 3. Firaol Gebi 694531-16
// 4. Yabsira Tesfaye 044683-16

// Note:
// This is only question upto 2 (Sorting and Searchs) still there are some modification needed.

#include <iostream>
#include <algorithm>

using namespace std;

const int MAX_SIZE = 100; // Maximum size of the array

void AcceptList(int List[], int Size)
{
    cout << "Enter " << Size << " elements: ";
    for (int i = 0; i < Size; i++)
    {
        cin >> List[i];
    }
}

int LinearSearch(int List[], int Size, int Key)
{
    for (int i = 0; i < Size; i++)
    {
        if (List[i] == Key)
        {
            return i;
        }
    }
    return -1;
}

int BinarySearch(int List[], int Size, int Key)
{
    int left = 0, right = Size - 1;
    while (left <= right)
    {
        int mid = (left + right) / 2;
        if (List[mid] == Key)
        {
            return mid;
        }
        else if (List[mid] < Key)
        {
            left = mid + 1;
        }
        else
        {
            right = mid - 1;
        }
    }
    return -1;
}

void InsertSort(int List[], int Size)
{
    for (int i = 1; i < Size; i++)
    {
        int key = List[i];
        int j = i - 1;
        while (j >= 0 && List[j] > key)
        {
            List[j + 1] = List[j];
            j--;
        }
        List[j + 1] = key;
    }
}

void SelectSort(int List[], int Size)
{
    for (int i = 0; i < Size - 1; i++)
    {
        int minIndex = i;
        for (int j = i + 1; j < Size; j++)
        {
            if (List[j] < List[minIndex])
            {
                minIndex = j;
            }
        }
        swap(List[i], List[minIndex]);
    }
}

void BubbleSort(int List[], int Size)
{
    for (int i = 0; i < Size - 1; i++)
    {
        for (int j = 0; j < Size - i - 1; j++)
        {
            if (List[j] > List[j + 1])
            {
                swap(List[j], List[j + 1]);
            }
        }
    }
}

void DisplayArray(int List[], int Size)
{
    cout << "Array: ";
    for (int i = 0; i < Size; i++)
    {
        cout << List[i] << " ";
    }
    cout << endl;
}

int Menu()
{
    cout << "\n=== Menu ===" << endl;
    cout << "1. Linear Search" << endl;
    cout << "2. Binary Search" << endl;
    cout << "3. Insertion Sort" << endl;
    cout << "4. Selection Sort" << endl;
    cout << "5. Bubble Sort" << endl;
    cout << "6. Exit" << endl;
    cout << "Enter your choice: ";
    int choice;
    cin >> choice;
    return choice;
}

int main()
{
    cout << "=== DSA Lab Exercises ===" << endl;

    // Get array size and elements at the beginning
    int arr[MAX_SIZE];
    int size, key, result;

    cout << "Enter size of array (max " << MAX_SIZE << "): ";
    cin >> size;

    if (size <= 0 || size > MAX_SIZE)
    {
        cout << "Invalid size!" << endl;
        return 1;
    }

    AcceptList(arr, size);
    cout << "Original ";
    DisplayArray(arr, size);

    // Create a copy of original array for operations
    int originalArr[MAX_SIZE];
    for (int i = 0; i < size; i++)
    {
        originalArr[i] = arr[i];
    }

    int choice;
    do
    {
        choice = Menu();

        // Restore original array before each operation
        for (int i = 0; i < size; i++)
        {
            arr[i] = originalArr[i];
        }

        switch (choice)
        {
        case 1: // Linear Search
            cout << "Enter element to search: ";
            cin >> key;
            result = LinearSearch(arr, size, key);
            if (result != -1)
            {
                cout << "Element found at index " << result << endl;
            }
            else
            {
                cout << "Element not found" << endl;
            }
            break;

        case 2: // Binary Search
            cout << "Sorting array first for binary search..." << endl;
            sort(arr, arr + size); // Sort the array first
            cout << "Sorted ";
            DisplayArray(arr, size);
            cout << "Enter element to search: ";
            cin >> key;
            result = BinarySearch(arr, size, key);
            if (result != -1)
            {
                cout << "Element found at index " << result << endl;
            }
            else
            {
                cout << "Element not found" << endl;
            }
            break;

        case 3: // Insertion Sort
            InsertSort(arr, size);
            cout << "Array after Insertion Sort:" << endl;
            DisplayArray(arr, size);
            break;

        case 4: // Selection Sort
            SelectSort(arr, size);
            cout << "Array after Selection Sort:" << endl;
            DisplayArray(arr, size);
            break;

        case 5: // Bubble Sort
            BubbleSort(arr, size);
            cout << "Array after Bubble Sort:" << endl;
            DisplayArray(arr, size);
            break;

        case 6:
            cout << "Exiting program. Thank you!" << endl;
            break;

        default:
            cout << "Invalid choice! Please try again." << endl;
        }
    } while (choice != 6);

    return 0;
}
